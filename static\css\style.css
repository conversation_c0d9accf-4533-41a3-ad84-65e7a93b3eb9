/* Kawaii Chat - Cute Theme CSS */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'N<PERSON><PERSON>', sans-serif;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.kawaii-container {
    min-height: 100vh;
    position: relative;
}

/* Floating Hearts Background */
.floating-hearts {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.heart {
    position: absolute;
    color: rgba(255, 107, 157, 0.6);
    font-size: 1.5rem;
    animation: floatUp 4s ease-out infinite;
}

@keyframes floatUp {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* Flash Messages */
.flash-messages {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.flash-message {
    background: white;
    border-radius: 15px;
    padding: 1rem 1.5rem;
    margin-bottom: 0.5rem;
    box-shadow: 0 4px 20px rgba(255, 107, 157, 0.3);
    border-left: 4px solid #ff6b9d;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    animation: slideInRight 0.3s ease-out;
    max-width: 400px;
}

.flash-success {
    border-left-color: #4caf50;
    background: linear-gradient(45deg, rgba(76, 175, 80, 0.1), rgba(255, 255, 255, 0.9));
}

.flash-error {
    border-left-color: #f44336;
    background: linear-gradient(45deg, rgba(244, 67, 54, 0.1), rgba(255, 255, 255, 0.9));
}

.flash-info {
    border-left-color: #2196f3;
    background: linear-gradient(45deg, rgba(33, 150, 243, 0.1), rgba(255, 255, 255, 0.9));
}

.close-flash {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 1rem;
    margin-left: auto;
    padding: 0.2rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-flash:hover {
    background: rgba(255, 107, 157, 0.1);
    color: #ff6b9d;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Authentication Pages */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
    z-index: 2;
}

.auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 25px;
    padding: 3rem;
    box-shadow: 0 20px 40px rgba(255, 107, 157, 0.2);
    border: 2px solid rgba(255, 107, 157, 0.1);
    max-width: 450px;
    width: 100%;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.auth-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="rgba(255,107,157,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,107,157,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,107,157,0.1)"/></svg>');
    animation: sparkle 20s linear infinite;
    pointer-events: none;
}

@keyframes sparkle {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Kawaii Mascot */
.kawaii-mascot {
    margin-bottom: 2rem;
    position: relative;
    z-index: 1;
}

.mascot-face {
    width: 80px;
    height: 80px;
    background: #fff;
    border-radius: 50%;
    position: relative;
    margin: 0 auto 1rem;
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.2);
    animation: bounce 2s ease-in-out infinite;
}

.mascot-face.large {
    width: 120px;
    height: 120px;
}

.mascot-face.excited {
    animation: excited 1s ease-in-out infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

@keyframes excited {
    0%, 100% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.1) rotate(-5deg); }
    75% { transform: scale(1.1) rotate(5deg); }
}

.eyes {
    display: flex;
    justify-content: space-between;
    position: absolute;
    top: 25px;
    left: 20px;
    right: 20px;
}

.eye {
    width: 10px;
    height: 15px;
    background: #333;
    border-radius: 50%;
    animation: blink 4s infinite;
}

.mascot-face.blink .eye {
    height: 2px;
}

@keyframes blink {
    0%, 90%, 100% { height: 15px; }
    95% { height: 2px; }
}

.mouth {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 25px;
    height: 12px;
    border: 3px solid #ff6b9d;
    border-top: none;
    border-radius: 0 0 25px 25px;
}

.mouth.happy {
    width: 30px;
    height: 15px;
    border-color: #4caf50;
}

.blush {
    position: absolute;
    width: 15px;
    height: 10px;
    background: #ff9999;
    border-radius: 50%;
    top: 35px;
}

.left-blush { left: 8px; }
.right-blush { right: 8px; }

.mascot-text {
    color: #c44569;
    font-size: 1.2rem;
    font-weight: 600;
}

/* Form Styles */
.auth-form {
    position: relative;
    z-index: 1;
}

.auth-title {
    color: #c44569;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.auth-title i {
    color: #ff6b9d;
    animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.form-group {
    margin-bottom: 1.5rem;
    text-align: left;
    position: relative;
}

.form-group label {
    display: block;
    color: #c44569;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-group input {
    width: 100%;
    padding: 1rem 1.5rem;
    border: 2px solid rgba(255, 107, 157, 0.2);
    border-radius: 15px;
    font-size: 1rem;
    font-family: 'Nunito', sans-serif;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    outline: none;
}

.form-group input:focus {
    border-color: #ff6b9d;
    background: white;
    box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
    transform: translateY(-2px);
}

.form-group.focused {
    transform: scale(1.02);
}

.form-hint {
    font-size: 0.8rem;
    color: #999;
    margin-top: 0.3rem;
}

.form-hint.success {
    color: #4caf50;
}

.form-hint.error {
    color: #f44336;
}

/* Buttons */
.btn-primary, .btn-secondary {
    padding: 1rem 2rem;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(45deg, #ff6b9d, #c44569);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 157, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.8);
    color: #c44569;
    border: 2px solid rgba(255, 107, 157, 0.3);
}

.btn-secondary:hover {
    background: white;
    border-color: #ff6b9d;
    transform: translateY(-2px);
}

.btn-sparkle {
    position: absolute;
    right: 1rem;
    animation: sparkleRotate 2s linear infinite;
}

@keyframes sparkleRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Auth Footer */
.auth-footer {
    margin-top: 2rem;
    position: relative;
    z-index: 1;
}

.auth-footer p {
    color: #666;
    margin-bottom: 0.5rem;
}

.auth-link {
    color: #ff6b9d;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    transition: all 0.3s ease;
}

.auth-link:hover {
    color: #c44569;
    transform: translateY(-1px);
}

/* Auth Navigation */
.auth-nav {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 107, 157, 0.2);
}

.auth-nav-btn {
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.9);
    color: #c44569;
    text-decoration: none;
    border-radius: 15px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: 2px solid transparent;
}

.auth-nav-btn:hover {
    background: rgba(255, 107, 157, 0.1);
    border-color: #ff6b9d;
    transform: translateY(-2px);
}

.auth-nav-btn.active {
    background: linear-gradient(45deg, #ff6b9d, #c44569);
    color: white;
    box-shadow: 0 2px 10px rgba(255, 107, 157, 0.3);
}

.auth-nav-btn.active:hover {
    background: linear-gradient(45deg, #e55a8a, #b83e5c);
    transform: translateY(-1px);
}

/* Decorative Elements */
.auth-decorations {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
}

.decoration {
    position: absolute;
    font-size: 2rem;
    opacity: 0.6;
    animation: float 3s ease-in-out infinite;
}

.decoration.star-1 { top: 10%; left: 10%; animation-delay: 0s; }
.decoration.star-2 { top: 20%; right: 15%; animation-delay: 1s; }
.decoration.heart-1 { bottom: 20%; left: 15%; animation-delay: 2s; }
.decoration.heart-2 { bottom: 10%; right: 10%; animation-delay: 0.5s; }
.decoration.unicorn { top: 50%; left: 5%; animation-delay: 1.5s; }
.decoration.rainbow { top: 60%; right: 5%; animation-delay: 2.5s; }
.decoration.cake { top: 80%; left: 20%; animation-delay: 3s; }
.decoration.gift { top: 30%; right: 25%; animation-delay: 3.5s; }

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(10deg); }
}

/* Animations */
.shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-10px); }
    75% { transform: translateX(10px); }
}

.heart-burst {
    position: fixed;
    font-size: 1.5rem;
    pointer-events: none;
    z-index: 1000;
    animation: heartBurst 1s ease-out forwards;
}

@keyframes heartBurst {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
    100% {
        transform: scale(0.8) translateY(-50px);
        opacity: 0;
    }
}

/* Profile Page */
.profile-container {
    min-height: 100vh;
    padding: 2rem;
    position: relative;
    z-index: 2;
}

.profile-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 1.5rem 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(255, 107, 157, 0.2);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-title {
    color: #c44569;
    font-size: 1.8rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.profile-nav {
    display: flex;
    gap: 1rem;
}

.nav-btn {
    padding: 0.75rem 1.5rem;
    background: linear-gradient(45deg, #ff6b9d, #c44569);
    color: white;
    text-decoration: none;
    border-radius: 15px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4);
}

.nav-btn.active {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    box-shadow: 0 2px 10px rgba(76, 175, 80, 0.3);
}

.nav-btn.active:hover {
    background: linear-gradient(45deg, #45a049, #3d8b40);
    transform: translateY(-1px);
}

.nav-btn.logout {
    background: linear-gradient(45deg, #f44336, #d32f2f);
}

.profile-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 25px;
    padding: 3rem;
    box-shadow: 0 20px 40px rgba(255, 107, 157, 0.2);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.profile-avatar-container {
    text-align: center;
    margin-bottom: 1rem;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
    position: relative;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    animation: profilePulse 2s ease-in-out infinite;
    overflow: hidden;
}

.profile-picture {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.change-avatar-btn {
    background: linear-gradient(45deg, #ff6b9d, #c44569);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.change-avatar-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 157, 0.4);
}

@keyframes profilePulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.avatar-decoration {
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 2rem;
    animation: sparkleRotate 3s linear infinite;
}

.profile-username {
    color: #c44569;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.profile-joined {
    color: #666;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.contact-code-section {
    background: linear-gradient(45deg, rgba(255, 107, 157, 0.1), rgba(254, 207, 239, 0.1));
    border-radius: 20px;
    padding: 2rem;
    margin: 2rem 0;
}

.section-title {
    color: #c44569;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.contact-code-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.contact-code {
    background: white;
    padding: 1rem 2rem;
    border-radius: 15px;
    font-size: 1.5rem;
    font-weight: 700;
    color: #c44569;
    letter-spacing: 2px;
    border: 2px solid rgba(255, 107, 157, 0.3);
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.1);
}

.copy-btn {
    padding: 1rem 1.5rem;
    background: linear-gradient(45deg, #4caf50, #45a049);
    color: white;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.copy-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
}

.copy-btn.success {
    background: linear-gradient(45deg, #2196f3, #1976d2);
}

.contact-code-hint {
    color: #666;
    font-style: italic;
}

.profile-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin: 2rem 0;
}

.stat-item {
    background: rgba(255, 107, 157, 0.1);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #c44569;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.quick-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.action-btn {
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.8);
    color: #c44569;
    border: 2px solid rgba(255, 107, 157, 0.3);
    border-radius: 15px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-btn:hover {
    background: white;
    border-color: #ff6b9d;
    transform: translateY(-2px);
}

.action-btn.active {
    background: #ff6b9d;
    color: white;
    border-color: #ff6b9d;
    box-shadow: 0 2px 8px rgba(255, 107, 157, 0.3);
}

.action-btn.active:hover {
    background: #e55a8a;
    border-color: #e55a8a;
    transform: translateY(-1px);
}

.profile-decorations {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.profile-decorations .decoration {
    position: absolute;
    font-size: 2rem;
    opacity: 0.4;
}

.bounce-1 { top: 10%; left: 10%; animation: bounce 2s ease-in-out infinite; }
.bounce-2 { top: 20%; right: 15%; animation: bounce 2s ease-in-out infinite 0.5s; }
.bounce-3 { bottom: 20%; left: 15%; animation: bounce 2s ease-in-out infinite 1s; }
.bounce-4 { bottom: 10%; right: 10%; animation: bounce 2s ease-in-out infinite 1.5s; }
.bounce-5 { top: 50%; right: 5%; animation: bounce 2s ease-in-out infinite 2s; }

/* Chat Page - Clean & Professional */
.chat-container {
    display: flex;
    height: calc(100vh - 60px);
    margin-top: 60px;
    background: #f0f2f5;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.chat-sidebar {
    width: 350px;
    background: #ffffff;
    border-right: 1px solid #e9edef;
    display: flex;
    flex-direction: column;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.sidebar-header {
    padding: 16px;
    border-bottom: 1px solid #e9edef;
    background: #f0f2f5;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.user-details h3 {
    color: #111b21;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 2px;
}

.user-status {
    color: #00a884;
    font-size: 13px;
    font-weight: 400;
}

.sidebar-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
    padding: 0.5rem;
}

.sidebar-actions .action-btn {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid #ff6b9d;
    color: #c44569;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(255, 107, 157, 0.2);
}

.contacts-section {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    display: flex;
    flex-direction: column;
}

.contacts-section .section-title {
    color: #c44569;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.contacts-list {
    display: flex;
    flex-direction: column;
    gap: 0;
    flex: 1;
    overflow-y: auto;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 0;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #ffffff;
    border-bottom: 1px solid #f0f2f5;
    position: relative;
}

.contact-item:hover {
    background: #f5f6f6;
}

.contact-item.active {
    background: #e7f3ff;
    border-left: 3px solid #00a884;
}

.contact-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
    overflow: hidden;
}

.contact-info {
    flex: 1;
    min-width: 0;
}

.contact-name {
    font-weight: 500;
    font-size: 16px;
    margin-bottom: 2px;
    color: #111b21;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.contact-status {
    font-size: 13px;
    color: #667781;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.contact-indicator {
    color: #ccc; /* Default to gray (offline) */
    font-size: 12px;
    margin-left: 8px;
}

.contact-indicator.online {
    color: #00a884; /* Green when online */
}

.contact-indicator.offline {
    color: #ccc; /* Gray when offline */
}

.no-contacts {
    text-align: center;
    padding: 2rem 1rem;
    color: #999;
}

.no-contacts i {
    font-size: 3rem;
    color: #ff6b9d;
    margin-bottom: 1rem;
    animation: heartbeat 2s ease-in-out infinite;
}

.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    position: relative;
}

.chat-view {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    height: 100vh; /* Full viewport height */
    height: 100dvh; /* Dynamic viewport height for mobile */
    position: relative;
    overflow: hidden; /* Prevent body scroll */
}

.chat-welcome {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 2rem;
    background: #f8f9fa;
}

.welcome-content {
    max-width: 500px;
}

.welcome-mascot {
    margin-bottom: 2rem;
    font-size: 4rem;
    color: #00a884;
}

.welcome-content h2 {
    color: #111b21;
    font-size: 2rem;
    margin-bottom: 1rem;
    font-weight: 300;
}

.welcome-content p {
    color: #667781;
    font-size: 1.1rem;
    margin-bottom: 2rem;
    line-height: 1.5;
}

.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    position: relative;
}

.chat-header {
    padding: 16px 20px;
    background: #ffffff;
    border-bottom: 2px solid #e9edef;
    display: flex;
    align-items: center;
    gap: 15px;
    min-height: 70px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: fixed; /* Fixed position like Facebook Messenger */
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.chat-contact-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    cursor: pointer;
}

.chat-contact-info .contact-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
    border: 2px solid #e9edef;
}

.contact-details h3 {
    color: #000000;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    line-height: 1.3;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.contact-details .contact-status {
    color: #00a884;
    font-size: 14px;
    margin: 0;
    line-height: 1.3;
    font-weight: 500;
}

.chat-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.chat-actions .action-btn {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 132, 255, 0.1);
    border: 2px solid rgba(0, 132, 255, 0.2);
    color: #0084ff;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.chat-actions .action-btn:hover {
    background: rgba(0, 132, 255, 0.2);
    border-color: rgba(0, 132, 255, 0.4);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 132, 255, 0.2);
}

.chat-actions .action-btn:active {
    transform: scale(0.95);
    box-shadow: 0 2px 4px rgba(0, 132, 255, 0.3);
}

.chat-actions .action-btn.video-call {
    color: #00a884;
    background: rgba(0, 168, 132, 0.1);
    border-color: rgba(0, 168, 132, 0.2);
}

.chat-actions .action-btn.video-call:hover {
    background: rgba(0, 168, 132, 0.2);
    border-color: rgba(0, 168, 132, 0.4);
    box-shadow: 0 4px 12px rgba(0, 168, 132, 0.2);
}

.chat-actions .action-btn.audio-call {
    color: #ff6b9d;
    background: rgba(255, 107, 157, 0.1);
    border-color: rgba(255, 107, 157, 0.2);
}

.chat-actions .action-btn.audio-call:hover {
    background: rgba(255, 107, 157, 0.2);
    border-color: rgba(255, 107, 157, 0.4);
    box-shadow: 0 4px 12px rgba(255, 107, 157, 0.2);
}

.chat-actions .action-btn.more-options {
    color: #666;
    background: rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0.1);
}

.chat-actions .action-btn.more-options:hover {
    background: rgba(0, 0, 0, 0.1);
    border-color: rgba(0, 0, 0, 0.2);
    color: #333;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    background: linear-gradient(135deg, #ffeef8 0%, #fff0f5 50%, #ffe4e1 100%);
    /* Messenger-style: Use flexbox, not fixed positioning */
    position: relative;
    min-height: 0;
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    scroll-behavior: smooth;
}

.messages-container::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext y='50' font-size='20' fill='%23ff69b4' opacity='0.15'%3E❤️%3C/text%3E%3C/svg%3E"),
        url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext y='50' font-size='16' fill='%23ff1493' opacity='0.12'%3E💕%3C/text%3E%3C/svg%3E"),
        url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext y='50' font-size='18' fill='%23ff69b4' opacity='0.10'%3E💖%3C/text%3E%3C/svg%3E"),
        url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext y='50' font-size='14' fill='%23ff1493' opacity='0.08'%3E💗%3C/text%3E%3C/svg%3E"),
        url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext y='50' font-size='22' fill='%23ff69b4' opacity='0.06'%3E🤍%3C/text%3E%3C/svg%3E"),
        url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext y='50' font-size='16' fill='%23ff1493' opacity='0.10'%3E💘%3C/text%3E%3C/svg%3E");
    background-size: 80px 80px, 60px 60px, 70px 70px, 50px 50px, 90px 90px, 65px 65px;
    background-position: 10px 20px, 150px 80px, 80px 150px, 200px 30px, 30px 120px, 180px 160px;
    background-repeat: repeat;
    pointer-events: none;
    animation: floatingHearts 20s linear infinite;
    z-index: -1; /* Behind everything */
}

@keyframes floatingHearts {
    0% {
        transform: translateY(0px);
    }
    100% {
        transform: translateY(-100px);
    }
}

/* Floating Emojis */
.floating-emojis {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 0; /* Behind messages */
    overflow: hidden;
}

.floating-emoji {
    position: absolute;
    font-size: 24px;
    opacity: 0.4;
    animation: floatUp 10s ease-out infinite;
    pointer-events: none;
    z-index: 5; /* Above background but below messages */
}

@keyframes floatUp {
    0% {
        transform: translateY(100vh) rotate(0deg) scale(0.5);
        opacity: 0;
    }
    10% {
        opacity: 0.4;
    }
    50% {
        opacity: 0.6;
    }
    90% {
        opacity: 0.2;
    }
    100% {
        transform: translateY(-100px) rotate(360deg) scale(1.2);
        opacity: 0;
    }
}

.message {
    display: flex;
    margin-bottom: 12px;
    animation: messageSlide 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    width: 100%;
    clear: both;
    position: relative;
    z-index: 10; /* Above floating emojis */
}

@keyframes messageSlide {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.message.own {
    justify-content: flex-end;
    align-items: flex-end;
}

.message:not(.own) {
    justify-content: flex-start;
    align-items: flex-start;
}

.message-bubble {
    max-width: 65%;
    padding: 8px 12px 8px 12px;
    border-radius: 7.5px;
    position: relative;
    word-wrap: break-word;
    box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);
    display: flex;
    flex-direction: column;
    z-index: 15; /* Above floating emojis and messages */
}

.message.own .message-bubble {
    background: rgba(217, 253, 211, 0.95);
    color: #111b21;
    border-bottom-right-radius: 2px;
    backdrop-filter: blur(1px);
}

.message:not(.own) .message-bubble {
    background: rgba(255, 255, 255, 0.95);
    color: #111b21;
    border-bottom-left-radius: 2px;
    backdrop-filter: blur(1px);
}

.message-sender {
    font-size: 12px;
    font-weight: 600;
    color: #00a884;
    margin-bottom: 2px;
    line-height: 1.2;
}

.message-text {
    font-size: 14px;
    line-height: 1.4;
    margin: 0;
    word-break: break-word;
}

.message-time {
    font-size: 11px;
    color: #667781;
    margin-top: 4px;
    text-align: right;
    opacity: 0.8;
    align-self: flex-end;
}

.message-text {
    font-size: 15px;
    line-height: 1.5;
    word-wrap: break-word;
    white-space: pre-wrap;
    margin-bottom: 4px;
}

.message-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 4px;
    gap: 8px;
}

.message-time {
    font-size: 11px;
    color: rgba(0, 0, 0, 0.45);
    font-weight: 500;
}

.message.own .message-time {
    color: rgba(255, 255, 255, 0.7);
}

.message-status {
    font-size: 12px;
    color: #666;
    margin-left: auto;
    font-weight: 500;
}

.message.own .message-status {
    color: #2d5016;
}

.message-status.sending {
    color: #999;
    animation: pulse 1s ease-in-out infinite;
}

.message-status.sent {
    color: #666;
    font-weight: 500;
}

.message-status.delivered {
    color: #00a884;
    font-weight: 600;
}

.message-status.read {
    color: #0084ff;
    font-weight: 600;
}

/* WhatsApp Style Image Messages */
.whatsapp-image-container {
    margin: 4px 0;
    max-width: 320px;
    position: relative;
    display: block;
    overflow: visible;
}

.image-wrapper {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    background: #f0f0f0;
}

.whatsapp-image {
    width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: cover;
    display: block;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.whatsapp-image:hover {
    transform: scale(1.02);
}

.image-overlay {
    position: absolute;
    bottom: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
    padding: 20px 8px 4px 20px;
    border-radius: 0 0 8px 0;
}

.image-time {
    color: white;
    font-size: 11px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
    font-weight: 400;
}

/* Reactions Container */
.reactions-container {
    position: relative;
    width: 100%;
    margin-top: 4px;
    z-index: 1;
}

/* WhatsApp Style Reactions */
.whatsapp-reactions {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    padding: 0 4px;
    position: relative;
    z-index: 1;
    clear: both;
}

.whatsapp-reaction-bubble {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    padding: 2px 6px;
    display: flex;
    align-items: center;
    gap: 2px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
    backdrop-filter: blur(5px);
}

.whatsapp-reaction-bubble:hover {
    background: rgba(255, 107, 157, 0.1);
    border-color: rgba(255, 107, 157, 0.3);
    transform: scale(1.05);
}

.whatsapp-reaction-bubble .reaction-emoji {
    font-size: 14px;
}

.whatsapp-reaction-bubble .reaction-count {
    font-size: 11px;
    color: #666;
    font-weight: 500;
    min-width: 8px;
    text-align: center;
}

.chat-image {
    width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: cover;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.2s ease;
    display: block;
}

.chat-image:hover {
    transform: scale(1.02);
}

/* WhatsApp Style Image Viewer */
.whatsapp-image-viewer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;
    z-index: 2000;
    display: flex;
    flex-direction: column;
}

.image-viewer-header {
    background: rgba(0, 0, 0, 0.8);
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 16px;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.back-btn {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background 0.2s ease;
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.image-info {
    flex: 1;
}

.sender-name {
    color: white;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 2px;
}

.image-date {
    color: rgba(255, 255, 255, 0.7);
    font-size: 13px;
}

.image-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background 0.2s ease;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.image-viewer-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.image-viewer-content img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
}

.image-viewer-footer {
    background: rgba(0, 0, 0, 0.8);
    padding: 12px 16px;
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 40px;
}

/* Image Viewer Reactions */
.viewer-reactions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.viewer-reaction-item {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 6px 12px;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.viewer-reaction-item:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
}

.viewer-reaction-item .reaction-emoji {
    font-size: 16px;
}

.viewer-reaction-item .reaction-count {
    color: white;
    font-size: 12px;
    font-weight: 500;
}

.viewer-reaction-item .reaction-users {
    color: rgba(255, 255, 255, 0.8);
    font-size: 11px;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Mobile responsive for images */
@media (max-width: 768px) {
    .image-message {
        max-width: 250px;
    }

    .chat-image {
        max-height: 300px;
    }

    .image-modal-content {
        max-width: 95%;
        max-height: 85%;
    }

    .image-modal-close {
        top: 10px;
        right: 15px;
        font-size: 30px;
    }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 2000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    animation: toastSlideIn 0.3s ease-out;
    display: flex;
    align-items: center;
    gap: 8px;
}

.toast.success {
    background: rgba(34, 197, 94, 0.9);
}

.toast.error {
    background: rgba(239, 68, 68, 0.9);
}

.toast.info {
    background: rgba(59, 130, 246, 0.9);
}

@keyframes toastSlideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes toastSlideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* WhatsApp Style Reaction Bar */
.whatsapp-reaction-bar {
    position: fixed;
    z-index: 2000;
    background: rgba(255, 255, 255, 0.98);
    border-radius: 25px;
    padding: 8px 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    animation: reactionBarSlideIn 0.2s ease-out;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.reaction-bar-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.reaction-emoji {
    font-size: 24px;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.reaction-emoji:hover {
    background: rgba(255, 107, 157, 0.1);
    transform: scale(1.2);
}

.reaction-more {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 4px;
}

.reaction-more:hover {
    background: rgba(255, 107, 157, 0.2);
    transform: scale(1.1);
}

.reaction-more i {
    font-size: 12px;
    color: #666;
}

/* More Reactions Modal */
.more-reactions-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2500;
    display: flex;
    align-items: center;
    justify-content: center;
}

.more-reactions-content {
    background: white;
    border-radius: 12px;
    padding: 20px;
    max-width: 400px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.reactions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #eee;
}

.reactions-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
}

.close-reactions {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.reactions-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 12px;
}

.reaction-option {
    font-size: 32px;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.reaction-option:hover {
    background: rgba(255, 107, 157, 0.1);
    transform: scale(1.1);
}

@keyframes reactionBarSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes reactionPop {
    0% {
        opacity: 0;
        transform: scale(0.5);
    }
    50% {
        opacity: 1;
        transform: scale(1.5);
    }
    100% {
        opacity: 0;
        transform: scale(1) translateY(-30px);
    }
}

/* Message Reactions Display */
.message-reactions {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 4px;
}

.reaction-item {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    padding: 2px 6px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 2px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.reaction-item:hover {
    background: rgba(255, 107, 157, 0.2);
    transform: scale(1.05);
}

.reaction-emoji {
    font-size: 14px;
}

.reaction-count {
    font-size: 11px;
    color: #666;
    font-weight: 500;
}

/* Mobile responsive for WhatsApp system */
@media (max-width: 768px) {
    .whatsapp-image-container {
        max-width: 280px;
    }

    .whatsapp-image {
        max-height: 300px;
    }

    .whatsapp-reaction-bar {
        padding: 6px 8px;
    }

    .reaction-emoji {
        font-size: 20px;
        width: 36px;
        height: 36px;
        padding: 6px;
    }

    .reaction-more {
        width: 28px;
        height: 28px;
    }

    .reactions-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 8px;
    }

    .reaction-option {
        font-size: 28px;
        padding: 8px;
    }

    .more-reactions-content {
        padding: 16px;
        width: 95%;
    }

    .image-viewer-header {
        padding: 8px 12px;
    }

    .sender-name {
        font-size: 14px;
    }

    .image-date {
        font-size: 12px;
    }

    .image-viewer-content {
        padding: 10px;
    }

    .viewer-reaction-item {
        padding: 4px 8px;
        font-size: 11px;
    }

    .viewer-reaction-item .reaction-users {
        max-width: 80px;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.7;
    }
    50% {
        opacity: 1;
    }
}

.message-sending {
    opacity: 0.7;
    animation: messageSending 1s ease-in-out infinite;
}

.message-sending .message-status {
    animation: spin 1s linear infinite;
}

@keyframes messageSending {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.9);
    }
}

/* Toast Notifications */
.toast-notification {
    position: fixed;
    top: 80px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    padding: 12px 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid #e9edef;
    z-index: 10000;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    max-width: 300px;
}

.toast-notification.toast-show {
    transform: translateX(0);
    opacity: 1;
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.toast-icon {
    font-size: 16px;
    flex-shrink: 0;
}

.toast-message {
    font-size: 14px;
    font-weight: 500;
    color: #111b21;
    line-height: 1.4;
}

.toast-success {
    border-color: #4caf50;
    background: rgba(232, 245, 233, 0.95);
}

.toast-error {
    border-color: #f44336;
    background: rgba(255, 235, 238, 0.95);
}

.toast-info {
    border-color: #2196f3;
    background: rgba(227, 242, 253, 0.95);
}

/* Media Messages */
.message-media {
    margin-bottom: 0.5rem;
    border-radius: 15px;
    overflow: hidden;
    max-width: 300px;
}

.message-media img {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 15px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.message-media img:hover {
    transform: scale(1.02);
}

.message-media video {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 15px;
}

.message-media audio {
    width: 100%;
    border-radius: 15px;
}

.file-message {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-message:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.file-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.file-details {
    flex: 1;
}

.file-name {
    font-weight: 600;
    margin-bottom: 0.2rem;
}

.file-size {
    font-size: 0.8rem;
    opacity: 0.7;
}

/* Upload Areas */
.upload-area {
    margin-bottom: 1rem;
}

.upload-zone {
    border: 3px dashed rgba(255, 107, 157, 0.3);
    border-radius: 15px;
    padding: 3rem 2rem;
    text-align: center;
    color: #999;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(255, 107, 157, 0.05);
}

.upload-zone:hover {
    border-color: #ff6b9d;
    background: rgba(255, 107, 157, 0.1);
    color: #ff6b9d;
}

.upload-zone i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #ff6b9d;
}

.upload-zone p {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.upload-zone small {
    font-size: 0.9rem;
    opacity: 0.7;
}

.media-preview, .file-preview {
    text-align: center;
}

.preview-container {
    margin-bottom: 1rem;
    border-radius: 15px;
    overflow: hidden;
    display: inline-block;
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.2);
}

.preview-container img {
    max-width: 100%;
    max-height: 300px;
    display: block;
}

.preview-container video {
    max-width: 100%;
    max-height: 300px;
    display: block;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 107, 157, 0.1);
    border-radius: 15px;
    margin-bottom: 1rem;
}

.caption-input {
    margin-top: 1rem;
}

.caption-input input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid rgba(255, 107, 157, 0.2);
    border-radius: 15px;
    font-size: 1rem;
    font-family: 'Nunito', sans-serif;
    outline: none;
    transition: all 0.3s ease;
}

.caption-input input:focus {
    border-color: #ff6b9d;
    box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
}

/* Profile Picture Upload */
.profile-preview {
    text-align: center;
}

.preview-avatar {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    margin: 0 auto 1rem;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(255, 107, 157, 0.3);
    border: 3px solid rgba(255, 107, 157, 0.2);
}

.preview-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-note {
    color: #666;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    background: rgba(255, 107, 157, 0.1);
    padding: 1rem;
    border-radius: 15px;
    margin-top: 1rem;
}

.preview-note i {
    color: #ff6b9d;
}

/* Update contact avatars to show profile pictures */
.contact-avatar img, .user-avatar img, .chat-contact-info .contact-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

/* Loading state for upload button */
.btn-loading {
    opacity: 0.7;
    cursor: not-allowed;
}

.btn-loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.message-input-area {
    padding: 12px;
    background: #f0f2f5;
    border-top: 1px solid #e9edef;
    position: sticky; /* Sticky like real Messenger - key change! */
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    min-height: 80px;
    /* Messenger-style: let it stick to bottom naturally */
    margin-top: auto;
}

.input-container {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #ffffff;
    border-radius: 24px;
    padding: 8px 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
    border: 1px solid #e9edef;
    transition: all 0.2s ease;
}

.input-container:focus-within {
    border-color: #00a884;
    box-shadow: 0 2px 8px rgba(0, 168, 132, 0.15);
}

.emoji-btn, .media-btn, .file-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: transparent;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #8696a0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.emoji-btn:hover, .media-btn:hover, .file-btn:hover {
    background: rgba(134, 150, 160, 0.15);
}

.media-btn {
    color: #8696a0;
}

.file-btn {
    color: #8696a0;
}

#messageInput {
    flex: 1;
    border: none;
    outline: none;
    padding: 8px 4px;
    font-size: 15px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    background: transparent;
    color: #333;
}

#messageInput::placeholder {
    color: #999;
}

.send-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 50%;
    background: #00a884;
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 4px;
}

.send-btn:hover {
    background: #008f72;
    transform: scale(1.05);
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Loading and Typing Indicators */
.messages-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #667781;
    font-size: 14px;
}

.loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #e9edef;
    border-top: 3px solid #00a884;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.typing-indicator {
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #667781;
    font-size: 13px;
    font-style: italic;
}

.typing-dots {
    display: flex;
    gap: 3px;
}

.typing-dots span {
    width: 4px;
    height: 4px;
    background: #00a884;
    border-radius: 50%;
    animation: typingDots 1.4s ease-in-out infinite both;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typingDots {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 9999;
    animation: fadeIn 0.3s ease-out;
}

.modal.show {
    display: flex !important;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 20px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 2px solid rgba(255, 107, 157, 0.1);
    background: linear-gradient(45deg, rgba(255, 107, 157, 0.1), rgba(254, 207, 239, 0.1));
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: #c44569;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.close-btn {
    width: 30px;
    height: 30px;
    border: none;
    background: transparent;
    font-size: 1.5rem;
    color: #999;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 107, 157, 0.1);
    color: #ff6b9d;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 2px solid rgba(255, 107, 157, 0.1);
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* Toast Notifications */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 15px;
    padding: 1rem 1.5rem;
    box-shadow: 0 4px 20px rgba(255, 107, 157, 0.3);
    border-left: 4px solid #ff6b9d;
    z-index: 1001;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.toast.show {
    transform: translateX(0);
}

.toast-success {
    border-left-color: #4caf50;
}

.toast-error {
    border-left-color: #f44336;
}

.toast-info {
    border-left-color: #2196f3;
}

/* Tablet Layout */
@media (min-width: 768px) and (max-width: 1024px) {
    .auth-container {
        padding: 1.5rem;
    }

    .auth-card {
        max-width: 500px;
        padding: 2.5rem;
    }

    .mascot-face {
        width: 100px;
        height: 100px;
    }

    .auth-title {
        font-size: 2rem;
    }

    .chat-container {
        max-width: 100%;
        margin: 0;
    }

    .chat-sidebar {
        width: 300px;
    }
}

/* Desktop Layout */
@media (min-width: 1025px) {
    /* Auth Pages Desktop Optimization */
    .auth-container {
        padding: 2rem;
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
        position: relative;
        overflow: hidden;
    }

    .auth-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.3)"/><circle cx="150" cy="100" r="3" fill="rgba(255,255,255,0.2)"/><circle cx="100" cy="150" r="2" fill="rgba(255,255,255,0.3)"/><circle cx="25" cy="125" r="1.5" fill="rgba(255,255,255,0.4)"/><circle cx="175" cy="25" r="2.5" fill="rgba(255,255,255,0.2)"/></svg>');
        animation: float 15s ease-in-out infinite;
        pointer-events: none;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        33% { transform: translateY(-20px) rotate(120deg); }
        66% { transform: translateY(10px) rotate(240deg); }
    }

    .auth-card {
        max-width: 550px;
        padding: 4rem;
        transform: scale(1);
        transition: transform 0.3s ease;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        border: 3px solid rgba(255, 107, 157, 0.2);
        box-shadow:
            0 25px 50px rgba(255, 107, 157, 0.15),
            0 0 0 1px rgba(255, 255, 255, 0.5) inset;
    }

    .auth-card:hover {
        transform: scale(1.02);
        box-shadow:
            0 30px 60px rgba(255, 107, 157, 0.2),
            0 0 0 1px rgba(255, 255, 255, 0.6) inset;
    }

    .mascot-face {
        width: 120px;
        height: 120px;
        box-shadow: 0 8px 25px rgba(255, 107, 157, 0.3);
    }

    .mascot-text {
        font-size: 1.4rem;
    }

    .auth-title {
        font-size: 2.2rem;
        margin-bottom: 2.5rem;
    }

    .form-group {
        margin-bottom: 2rem;
    }

    .form-group input {
        font-size: 1.1rem;
        padding: 1rem 1.2rem;
    }

    .btn-primary {
        font-size: 1.2rem;
        padding: 1rem 2rem;
        min-width: 200px;
    }

    /* Chat Desktop Layout */
    .chat-container {
        max-width: 1400px;
        margin: 0 auto;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border-radius: 15px;
        overflow: hidden;
        background: #ffffff;
        height: 100vh;
        display: flex;
    }

    .chat-sidebar {
        width: 380px;
        flex-shrink: 0;
        border-right: 2px solid #e9edef;
        background: #f8f9fa;
    }

    .chat-area {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
        height: 100vh;
    }

    .chat-back-btn {
        display: none !important;
    }

    .chat-header {
        background: linear-gradient(135deg, #ff6b9d, #c44569);
        color: white;
        border-bottom: none;
        padding: 20px 24px;
        min-height: 80px;
        display: flex;
        align-items: center;
        gap: 15px;
        box-shadow: 0 2px 15px rgba(255, 107, 157, 0.2);
        position: relative;
        flex-shrink: 0;
    }

    .chat-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
        pointer-events: none;
    }

    .messages-container {
        padding: 24px;
        background: linear-gradient(135deg, #ffeef4 0%, #fff0f5 100%);
        background-image:
            radial-gradient(circle at 20% 50%, rgba(255, 107, 157, 0.05) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(196, 69, 105, 0.05) 0%, transparent 50%),
            radial-gradient(circle at 40% 80%, rgba(255, 107, 157, 0.03) 0%, transparent 50%);
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        gap: 12px;
        flex: 1;
        scrollbar-width: thin;
        scrollbar-color: rgba(255, 107, 157, 0.3) transparent;
    }

    .messages-container::-webkit-scrollbar {
        width: 8px;
    }

    .messages-container::-webkit-scrollbar-track {
        background: rgba(255, 107, 157, 0.1);
        border-radius: 10px;
    }

    .messages-container::-webkit-scrollbar-thumb {
        background: rgba(255, 107, 157, 0.3);
        border-radius: 10px;
    }

    .messages-container::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 107, 157, 0.5);
    }

    .message-input-area {
        background: linear-gradient(135deg, #f8f9fa, #ffffff);
        padding: 20px 24px;
        border-top: 2px solid rgba(255, 107, 157, 0.1);
        flex-shrink: 0;
        box-shadow: 0 -2px 15px rgba(0, 0, 0, 0.05);
    }

    /* Desktop Decorative Elements */
    .auth-decorations .decoration {
        font-size: 2.5rem;
        opacity: 0.7;
    }

    .decoration.star-1 {
        top: 8%;
        left: 8%;
        animation: float 4s ease-in-out infinite, sparkle 2s ease-in-out infinite;
    }

    .decoration.star-2 {
        top: 15%;
        right: 12%;
        animation: float 3.5s ease-in-out infinite reverse, sparkle 2.5s ease-in-out infinite;
    }

    .decoration.heart-1 {
        bottom: 25%;
        left: 12%;
        animation: float 3s ease-in-out infinite, heartbeat 1.5s ease-in-out infinite;
    }

    .decoration.heart-2 {
        bottom: 15%;
        right: 8%;
        animation: float 3.8s ease-in-out infinite reverse, heartbeat 2s ease-in-out infinite;
    }

    .decoration.unicorn {
        top: 45%;
        left: 3%;
        animation: float 4.5s ease-in-out infinite, bounce 2s ease-in-out infinite;
    }

    .decoration.rainbow {
        top: 55%;
        right: 3%;
        animation: float 3.2s ease-in-out infinite reverse, rainbow-glow 3s ease-in-out infinite;
    }

    @keyframes sparkle {
        0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.7; }
        50% { transform: scale(1.2) rotate(180deg); opacity: 1; }
    }

    @keyframes rainbow-glow {
        0%, 100% { filter: hue-rotate(0deg); }
        50% { filter: hue-rotate(180deg); }
    }

    /* Desktop Message Bubbles Enhancement */
    .message.sent {
        background: linear-gradient(135deg, #ff6b9d, #e55a8a);
        box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
        transform: translateX(0);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .message.sent:hover {
        transform: translateX(-5px);
        box-shadow: 0 6px 20px rgba(255, 107, 157, 0.4);
    }

    .message.received {
        background: linear-gradient(135deg, #ffffff, #f8f9fa);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 107, 157, 0.1);
        transform: translateX(0);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .message.received:hover {
        transform: translateX(5px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        border-color: rgba(255, 107, 157, 0.2);
    }

    /* Desktop Input Enhancement */
    .message-input {
        background: linear-gradient(135deg, #ffffff, #fafbfc);
        border: 2px solid rgba(255, 107, 157, 0.2);
        border-radius: 25px;
        padding: 12px 20px;
        font-size: 1rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(255, 107, 157, 0.1);
    }

    .message-input:focus {
        border-color: #ff6b9d;
        box-shadow: 0 4px 20px rgba(255, 107, 157, 0.2);
        background: #ffffff;
    }

    .send-btn {
        background: linear-gradient(135deg, #ff6b9d, #c44569);
        border: none;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
    }

    .send-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(255, 107, 157, 0.4);
    }

    .send-btn:active {
        transform: scale(0.95);
    }

    /* Desktop Sidebar Enhancement */
    .sidebar-header {
        background: linear-gradient(135deg, #ff6b9d, #c44569);
        color: white;
        padding: 20px;
        text-align: center;
        box-shadow: 0 2px 10px rgba(255, 107, 157, 0.2);
    }

    .sidebar-header h2 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 700;
    }

    .contacts-list {
        padding: 10px;
        background: #f8f9fa;
    }

    .contact-item {
        padding: 15px;
        margin: 5px 0;
        background: white;
        border-radius: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 107, 157, 0.1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .contact-item:hover {
        background: linear-gradient(135deg, #fff0f5, #ffffff);
        border-color: rgba(255, 107, 157, 0.3);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(255, 107, 157, 0.1);
    }

    .contact-item.active {
        background: linear-gradient(135deg, #ff6b9d, #e55a8a);
        color: white;
        border-color: #c44569;
        box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
    }

    /* Desktop Profile Enhancement */
    .profile-container {
        max-width: 600px;
        margin: 2rem auto;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 3rem;
        box-shadow: 0 20px 40px rgba(255, 107, 157, 0.15);
        border: 2px solid rgba(255, 107, 157, 0.1);
    }

    .profile-avatar {
        width: 150px;
        height: 150px;
        margin: 0 auto 2rem;
        border: 4px solid #ff6b9d;
        box-shadow: 0 8px 25px rgba(255, 107, 157, 0.3);
    }

    .profile-form .form-group {
        margin-bottom: 2rem;
    }

    .profile-form input, .profile-form textarea {
        font-size: 1.1rem;
        padding: 1rem 1.2rem;
        border: 2px solid rgba(255, 107, 157, 0.2);
        border-radius: 15px;
        transition: all 0.3s ease;
    }

    .profile-form input:focus, .profile-form textarea:focus {
        border-color: #ff6b9d;
        box-shadow: 0 4px 20px rgba(255, 107, 157, 0.2);
    }

    /* Desktop Animation Enhancements */
    @keyframes desktopFadeIn {
        from {
            opacity: 0;
            transform: translateY(30px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .auth-card {
        animation: desktopFadeIn 0.8s ease-out;
    }

    .message {
        animation: desktopFadeIn 0.5s ease-out;
    }

    /* Desktop Responsive Adjustments */
    @media (min-width: 1440px) {
        .chat-container {
            max-width: 1600px;
        }

        .chat-sidebar {
            width: 420px;
        }

        .auth-card {
            max-width: 600px;
            padding: 4.5rem;
        }

        .mascot-face {
            width: 140px;
            height: 140px;
        }

        .auth-title {
            font-size: 2.5rem;
        }
    }
}

/* Mobile Optimizations */
@media (max-width: 767px) {
    .auth-container {
        padding: 1rem;
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    }

    .auth-card {
        padding: 2rem 1.5rem;
        margin: 0;
        border-radius: 20px;
        max-width: 100%;
    }

    .mascot-face {
        width: 80px;
        height: 80px;
    }

    .auth-title {
        font-size: 1.6rem;
        margin-bottom: 1.5rem;
    }

    .form-group {
        margin-bottom: 1.2rem;
    }

    .form-group input {
        padding: 0.9rem 1rem;
        font-size: 1rem;
    }

    .btn-primary {
        padding: 0.9rem 1.5rem;
        font-size: 1rem;
        width: 100%;
    }

    .decoration {
        font-size: 1.5rem !important;
        opacity: 0.4 !important;
    }

    /* Mobile Chat Optimizations */
    .chat-container {
        height: 100vh;
        border-radius: 0;
    }

    .chat-header {
        padding: 15px;
        min-height: 70px;
        background: linear-gradient(135deg, #ff6b9d, #c44569);
        color: white;
    }

    .messages-container {
        padding: 15px;
        background: linear-gradient(135deg, #ffeef4 0%, #fff0f5 100%);
    }

    .message-input-area {
        padding: 15px;
        background: linear-gradient(135deg, #f8f9fa, #ffffff);
    }

    .message-input {
        border-radius: 20px;
        padding: 10px 15px;
        font-size: 0.95rem;
    }

    .send-btn {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }

    .message {
        max-width: 85%;
        padding: 12px 16px;
        margin-bottom: 8px;
    }

    .message.sent {
        background: linear-gradient(135deg, #ff6b9d, #e55a8a);
        box-shadow: 0 2px 10px rgba(255, 107, 157, 0.2);
    }

    .message.received {
        background: white;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 107, 157, 0.1);
    }
}

/* Small Mobile Devices */
@media (max-width: 480px) {
    .auth-card {
        padding: 1.5rem 1rem;
        border-radius: 15px;
    }

    .mascot-face {
        width: 70px;
        height: 70px;
    }

    .auth-title {
        font-size: 1.4rem;
    }

    .mascot-text {
        font-size: 1rem;
    }

    .decoration {
        font-size: 1.2rem !important;
        opacity: 0.3 !important;
    }

    .message {
        max-width: 90%;
        padding: 10px 14px;
        font-size: 0.9rem;
    }

    .chat-header {
        padding: 12px;
        min-height: 65px;
    }

    .messages-container {
        padding: 12px;
    }

    .message-input-area {
        padding: 12px;
    }
}
        min-height: 90px;
    }

    .message {
        display: flex;
        width: 100%;
        margin-bottom: 8px;
    }

    .message.own {
        justify-content: flex-end;
    }

    .message:not(.own) {
        justify-content: flex-start;
    }

    .message-bubble {
        max-width: 60%;
        padding: 8px 12px;
        border-radius: 7.5px;
        font-size: 14px;
        line-height: 1.4;
        word-wrap: break-word;
        box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);
    }

    .message.own .message-bubble {
        background: #d9fdd3;
        color: #111b21;
        border-bottom-right-radius: 2px;
    }

    .message:not(.own) .message-bubble {
        background: #ffffff;
        color: #111b21;
        border-bottom-left-radius: 2px;
    }

    /* Hide back button on desktop */
    .chat-back-btn {
        display: none !important;
    }

    /* Welcome screen styling */
    .chat-welcome {
        background: #f8f9fa;
        border-left: 1px solid #e9edef;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/* Mobile Layout - Facebook Messenger Style */
@media (max-width: 768px) {
    .chat-container {
        flex-direction: column;
        height: 100vh;
        height: 100dvh;
    }

    .chat-sidebar {
        width: 100%;
        height: auto;
        max-height: none;
        border-right: none;
        border-bottom: 1px solid #e4e6ea;
        background: #ffffff;
    }

    .chat-area {
        width: 100%;
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    /* Hide sidebar when chat is open on mobile */
    .chat-fullscreen .chat-sidebar {
        display: none !important;
    }

    /* Make chat area take full screen when active */
    .chat-fullscreen {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        height: 100dvh !important;
        z-index: 2000 !important;
        background: #ffffff !important;
        display: flex !important;
        flex-direction: column !important;
        margin: 0 !important;
        padding: 0 !important;
    }
}

    .auth-card {
        margin: 1rem;
        padding: 2rem;
    }

    .profile-container {
        padding: 1rem;
    }

    .profile-stats {
        grid-template-columns: 1fr;
    }

    .quick-actions {
        flex-direction: column;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 107, 157, 0.1);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #ff6b9d, #c44569);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #c44569, #ff6b9d);
}

/* Professional Navigation Menu */
.main-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1001;
    background: #ffffff;
    border-bottom: 1px solid #e0e0e0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.3rem;
    font-weight: 600;
    color: #333333;
}

.nav-brand i {
    font-size: 1.2rem;
    color: #007bff;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    gap: 0.25rem;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: #333333;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    color: #666666;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 0.95rem;
}

.nav-link:hover {
    background: #f8f9fa;
    color: #007bff;
}

.nav-link.active {
    background: #007bff;
    color: white;
}

.nav-link.logout {
    color: #dc3545;
}

.nav-link.logout:hover {
    background: #f8f9fa;
    color: #c82333;
}

/* Adjust main content for fixed nav - handled in main declaration */

/* Mobile Chat Header - Facebook Messenger Style */
@media (max-width: 768px) {
    .chat-header {
        position: sticky !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        z-index: 1001 !important;
        background: #ffffff !important;
        border-bottom: 1px solid #e4e6ea !important;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
        padding: 12px 16px !important;
        min-height: 60px !important;
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
    }

    .chat-back-btn {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 40px !important;
        height: 40px !important;
        border-radius: 50% !important;
        background: none !important;
        border: none !important;
        color: #1877f2 !important;
        font-size: 18px !important;
        cursor: pointer !important;
        transition: background-color 0.2s ease !important;
    }

    .chat-back-btn:hover {
        background: rgba(24, 119, 242, 0.1) !important;
    }

    .chat-contact-info {
        flex: 1 !important;
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
        cursor: pointer !important;
    }

    .chat-contact-info .contact-avatar {
        width: 40px !important;
        height: 40px !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 16px !important;
        color: white !important;
    }

    .chat-contact-info .contact-details h3 {
        font-size: 16px !important;
        font-weight: 600 !important;
        color: #050505 !important;
        margin: 0 !important;
        line-height: 1.2 !important;
    }

    .chat-contact-info .contact-details .contact-status {
        font-size: 13px !important;
        color: #65676b !important;
        margin: 0 !important;
        line-height: 1.2 !important;
    }

    .chat-actions {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
    }

    .chat-actions .action-btn {
        width: 36px !important;
        height: 36px !important;
        border-radius: 50% !important;
        background: none !important;
        border: none !important;
        color: #1877f2 !important;
        font-size: 16px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        cursor: pointer !important;
        transition: background-color 0.2s ease !important;
    }

    .chat-actions .action-btn:hover {
        background: rgba(24, 119, 242, 0.1) !important;
    }
}

.chat-fullscreen .chat-header {
    padding: 1rem !important;
    border-bottom: 1px solid #e0e0e0 !important;
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important;
    background: white !important;
    flex-shrink: 0 !important;
}

.chat-back-btn {
    background: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #000000;
    font-size: 22px;
    flex-shrink: 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chat-back-btn:hover {
    background: rgba(0, 132, 255, 0.1);
    border-color: rgba(0, 132, 255, 0.3);
    color: #0084ff;
    transform: scale(1.05);
}

.chat-back-btn:active {
    background: rgba(0, 132, 255, 0.2);
    transform: scale(0.95);
}

.chat-fullscreen .messages-container {
    flex: 1;
    overflow-y: auto;
}

.chat-fullscreen .message-input-area {
    border-top: 1px solid #e0e0e0;
    padding: 1rem;
}

/* Facebook Messenger Style - Simple and Effective */
/* Let the browser handle keyboard naturally with sticky positioning */

/* Mobile - Keep it simple like Messenger */
@media (max-width: 768px) {
    .chat-view {
        height: 100vh;
        height: 100dvh; /* Dynamic viewport height */
    }
}

/* Remove all complex keyboard handling - let browser do it naturally */

/* Mobile Responsive Styles */
@media (max-width: 768px) {
    .nav-toggle {
        display: flex !important;
        z-index: 1002;
    }

    .nav-menu {
        position: fixed !important;
        top: 60px;
        left: 0;
        right: 0;
        background: #ffffff;
        border-bottom: 1px solid #e0e0e0;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        flex-direction: column;
        padding: 1rem 0;
        gap: 0;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1001;
        max-height: calc(100vh - 60px);
        overflow-y: auto;
    }

    .nav-menu.active {
        transform: translateY(0) !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    .nav-link {
        width: 100%;
        justify-content: flex-start;
        padding: 1rem 1.5rem;
        border-radius: 0;
        font-size: 1rem;
        min-height: 48px;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        background: none;
        border: none;
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.2s ease;
        font-weight: 500;
        color: #666666;
    }

    .nav-link:hover {
        background: #f8f9fa;
        color: #007bff;
    }

    .nav-link.active {
        background: #007bff;
        color: white;
    }

    .nav-link.logout {
        color: #dc3545;
        border-bottom: none;
    }

    .nav-link.logout:hover {
        background: #f8f9fa;
        color: #c82333;
    }

    .nav-link i {
        font-size: 1.1rem;
        width: 20px;
        text-align: center;
    }



    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    /* Professional nav toggle */
    .nav-toggle {
        position: relative;
        z-index: 1003;
        background: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        padding: 0.5rem;
        width: 44px;
        height: 44px;
        cursor: pointer;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .nav-toggle:hover {
        background: #f8f9fa;
        border-color: #007bff;
    }

    .nav-toggle:active {
        transform: scale(0.98);
        background: #e9ecef;
    }

    .nav-toggle span {
        display: block;
        width: 22px;
        height: 2px;
        background: #333333;
        margin: 4px 0;
        transition: 0.3s;
        border-radius: 1px;
    }

    /* Add overlay to prevent clicks behind menu */
    .nav-menu.active::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.3);
        z-index: -1;
    }
}
    /* Chat Layout */
    .chat-container {
        flex-direction: column;
        height: calc(100vh - 60px); /* Account for top navigation */
    }

    /* Sidebar Mobile - Facebook Messenger Style */
    .chat-sidebar {
        width: 100% !important;
        height: auto !important;
        max-height: none !important;
        border-radius: 0 !important;
        border-bottom: 1px solid #e4e6ea !important;
        background: #ffffff !important;
        display: flex !important;
        flex-direction: column !important;
    }

    .sidebar-header {
        padding: 16px !important;
        border-bottom: 1px solid #e4e6ea !important;
        background: #ffffff !important;
    }

    .user-info {
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
    }

    .user-avatar {
        width: 40px !important;
        height: 40px !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 16px !important;
        color: white !important;
    }

    .user-details h3 {
        font-size: 16px !important;
        font-weight: 600 !important;
        color: #050505 !important;
        margin: 0 !important;
    }

    .user-status {
        font-size: 13px !important;
        color: #65676b !important;
        margin: 0 !important;
    }

    /* Hide sidebar actions on mobile since we have top nav menu */
    .sidebar-actions {
        display: none !important;
    }

    /* Contacts Section Mobile - Facebook Messenger Style */
    .contacts-section {
        padding: 0 !important;
        background: #ffffff !important;
        flex: 1 !important;
        overflow-y: auto !important;
        max-height: none !important;
    }

    .section-title {
        padding: 12px 16px 8px 16px !important;
        font-size: 14px !important;
        font-weight: 600 !important;
        color: #65676b !important;
        margin: 0 !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
    }

    .contacts-list {
        padding: 0 !important;
    }

    .contact-item {
        padding: 12px 16px !important;
        margin: 0 !important;
        border-radius: 0 !important;
        border-bottom: 1px solid #f0f2f5 !important;
        background: #ffffff !important;
        cursor: pointer !important;
        transition: background-color 0.2s ease !important;
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
    }

    .contact-item:hover {
        background: #f0f2f5 !important;
    }

    .contact-item:active {
        background: #e4e6ea !important;
    }

    .contact-avatar {
        width: 56px !important;
        height: 56px !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 20px !important;
        color: white !important;
        flex-shrink: 0 !important;
    }

    .contact-info {
        flex: 1 !important;
        min-width: 0 !important;
    }

    .contact-name {
        font-size: 15px !important;
        font-weight: 500 !important;
        color: #050505 !important;
        margin: 0 0 2px 0 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }

    .contact-status {
        font-size: 13px !important;
        color: #65676b !important;
        margin: 0 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }

    .contact-indicator {
        flex-shrink: 0 !important;
        width: 12px !important;
        height: 12px !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .contact-indicator i {
        font-size: 8px !important;
    }

    /* Chat Area Mobile - Handled by Facebook Messenger styles above */

    /* Messages Container - Facebook Messenger Style */
    .messages-container {
        flex: 1 !important;
        padding: 8px 16px !important;
        background: #ffffff !important;
        overflow-y: auto !important;
        display: flex !important;
        flex-direction: column !important;
    }

    .message {
        max-width: 75% !important;
        margin-bottom: 8px !important;
        display: flex !important;
        flex-direction: column !important;
    }

    .message.own {
        align-self: flex-end !important;
    }

    .message:not(.own) {
        align-self: flex-start !important;
    }

    .message-bubble {
        padding: 8px 12px !important;
        border-radius: 18px !important;
        font-size: 15px !important;
        line-height: 1.33 !important;
        word-wrap: break-word !important;
    }

    .message.own .message-bubble {
        background: #0084ff !important;
        color: #ffffff !important;
    }

    .message:not(.own) .message-bubble {
        background: #f0f0f0 !important;
        color: #050505 !important;
    }

    .message-time {
        font-size: 11px !important;
        color: #65676b !important;
        margin-top: 2px !important;
        text-align: center !important;
    }

    /* Message Input Area - Facebook Messenger Style */
    .message-input-area {
        position: sticky !important;
        bottom: 0 !important;
        z-index: 1000 !important;
        background: #ffffff !important;
        border-top: 1px solid #e4e6ea !important;
        padding: 8px 16px !important;
    }

    .input-container {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        background: #f0f2f5 !important;
        border-radius: 20px !important;
        padding: 6px 12px !important;
    }

    .emoji-btn {
        width: 32px !important;
        height: 32px !important;
        border-radius: 50% !important;
        background: none !important;
        border: none !important;
        font-size: 18px !important;
        cursor: pointer !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    #messageInput {
        flex: 1 !important;
        border: none !important;
        background: none !important;
        outline: none !important;
        font-size: 15px !important;
        padding: 8px 4px !important;
        color: #050505 !important;
    }

    #messageInput::placeholder {
        color: #65676b !important;
    }

    .media-btn, .file-btn {
        width: 32px !important;
        height: 32px !important;
        border-radius: 50% !important;
        background: none !important;
        border: none !important;
        color: #1877f2 !important;
        font-size: 16px !important;
        cursor: pointer !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        transition: background-color 0.2s ease !important;
    }

    .media-btn:hover, .file-btn:hover {
        background: rgba(24, 119, 242, 0.1) !important;
    }

    .send-btn {
        width: 32px !important;
        height: 32px !important;
        border-radius: 50% !important;
        background: #1877f2 !important;
        border: none !important;
        color: #ffffff !important;
        font-size: 14px !important;
        cursor: pointer !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        transition: background-color 0.2s ease !important;
    }

    .send-btn:hover {
        background: #166fe5 !important;
    }

    .send-btn:disabled {
        background: #e4e6ea !important;
        color: #bcc0c4 !important;
        cursor: not-allowed !important;
    }

    /* Profile Page Mobile */
    .profile-nav {
        flex-wrap: wrap;
        gap: 0.75rem;
        justify-content: center;
        padding: 1rem;
    }

    .nav-btn {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
        min-width: 120px;
        border-radius: 20px;
    }

    .profile-card {
        margin: 1rem;
        padding: 2rem;
        border-radius: 25px;
    }

    /* Modal Mobile */
    .modal-content {
        width: 95%;
        margin: 1rem;
        max-height: 90vh;
        overflow-y: auto;
        border-radius: 25px;
    }

    .modal-footer {
        padding: 1.5rem;
        flex-direction: column;
        gap: 1rem;
    }

    .modal-footer .btn-primary,
    .modal-footer .btn-secondary {
        width: 100%;
        padding: 1rem;
        font-size: 1.1rem;
        border-radius: 20px;
    }

    /* Welcome Screen Mobile */
    .chat-welcome {
        padding: 2rem 1rem;
    }

    .btn-primary {
        padding: 1.2rem 2rem;
        font-size: 1.1rem;
        min-height: 55px;
        border-radius: 20px;
    }
}

/* Extra Small Mobile Devices */
@media (max-width: 480px) {
    .sidebar-actions .action-btn {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .message {
        max-width: 90%;
        padding: 0.8rem;
    }

    .modal-content {
        width: 98%;
        margin: 0.5rem;
    }
}

/* Message Selection Styles */
.message-selected {
    background-color: rgba(0, 132, 255, 0.1) !important;
    border-left: 4px solid #0084ff !important;
}

.message-selectable {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.message-selectable:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.selection-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: #0084ff;
    color: white;
    padding: 15px 20px;
    display: none;
    align-items: center;
    justify-content: space-between;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.selection-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.selection-count {
    font-weight: 600;
    font-size: 16px;
}

.selection-actions {
    display: flex;
    gap: 15px;
}

.selection-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.2s ease;
}

.selection-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.selection-btn i {
    font-size: 14px;
}

/* Reply Preview Styles */
.reply-preview {
    background: #f8f9fa;
    border-left: 4px solid #0084ff;
    padding: 12px 16px;
    margin-bottom: 8px;
    border-radius: 8px 8px 0 0;
    animation: slideDown 0.2s ease-out;
}

.reply-content {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.reply-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #0084ff;
    font-weight: 500;
}

.reply-header i {
    font-size: 12px;
}

.reply-close {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    margin-left: auto;
    transition: background-color 0.2s ease;
}

.reply-close:hover {
    background: rgba(0, 0, 0, 0.1);
}

.reply-message {
    font-size: 14px;
    color: #333;
    max-height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    line-height: 1.4;
}

.reply-message.image-reply {
    display: flex;
    align-items: center;
    gap: 8px;
    font-style: italic;
    color: #666;
}

.reply-message.image-reply i {
    font-size: 16px;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Simple Emoji Picker */
.simple-emoji-picker {
    position: fixed;
    bottom: 90px;
    left: 20px;
    width: 320px;
    height: 350px;
    background: white;
    border: 2px solid #ddd;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    z-index: 9999;
    display: none;
    flex-direction: column;
}

.emoji-header {
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.emoji-content {
    padding: 15px;
    overflow-y: auto;
    flex: 1;
}

.emoji-section {
    margin-bottom: 20px;
}

.emoji-section-title {
    font-size: 14px;
    font-weight: 600;
    color: #666;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    gap: 5px;
}

.emoji-section:first-child .emoji-section-title {
    color: #0084ff;
    font-weight: 700;
}

.emoji-section:first-child {
    background: rgba(0, 132, 255, 0.02);
    padding: 10px;
    border-radius: 8px;
    margin-bottom: 15px;
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 8px;
}

.emoji-item {
    background: none;
    border: none;
    font-size: 24px;
    padding: 8px;
    cursor: pointer;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.emoji-item:hover {
    background: #f0f0f0;
    transform: scale(1.1);
}

/* File Upload Styles */
.file-preview-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.file-icon {
    font-size: 32px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.file-details {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
    word-break: break-word;
}

.file-meta {
    font-size: 12px;
    color: #666;
}

.remove-file-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.remove-file-btn:hover {
    background: #c82333;
}

.caption-input {
    margin-top: 15px;
}

.caption-input input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
}

.caption-input input:focus {
    outline: none;
    border-color: #0084ff;
    box-shadow: 0 0 0 2px rgba(0, 132, 255, 0.1);
}

/* File Message Styles */
.file-message-container {
    max-width: 300px;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 12px;
    border: 1px solid #e9ecef;
}

.file-message-content {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    padding: 8px;
    border-radius: 8px;
}

.file-message-content:hover {
    background: rgba(0, 132, 255, 0.05);
}

.file-message-container .file-icon {
    font-size: 28px;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.file-message-container .file-details {
    flex: 1;
    min-width: 0;
}

.file-message-container .file-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
    word-break: break-word;
    font-size: 14px;
}

.file-message-container .file-meta {
    font-size: 11px;
    color: #666;
}

.file-download {
    color: #0084ff;
    font-size: 18px;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.file-message-content:hover .file-download {
    opacity: 1;
}

.file-caption {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #e9ecef;
    font-size: 14px;
    color: #333;
    word-break: break-word;
}

/* File message in own messages */
.message.own .file-message-container {
    background: rgba(0, 132, 255, 0.1);
    border-color: rgba(0, 132, 255, 0.2);
}

.message.own .file-message-content:hover {
    background: rgba(0, 132, 255, 0.15);
}

/* Video Message Styles */
.video-message-container {
    max-width: 300px;
    background: #f8f9fa;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.video-thumbnail {
    position: relative;
    width: 100%;
    height: 200px;
    cursor: pointer;
    overflow: hidden;
    background: #000;
}

.video-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.2s ease;
}

.video-thumbnail:hover .video-preview {
    transform: scale(1.05);
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

.video-thumbnail:hover .video-overlay {
    background: rgba(0, 0, 0, 0.5);
}

.play-button {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #333;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.video-thumbnail:hover .play-button {
    background: white;
    transform: scale(1.1);
}

.video-duration {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.video-info {
    padding: 12px;
}

.video-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
    word-break: break-word;
    font-size: 14px;
}

.video-size {
    font-size: 12px;
    color: #666;
}

/* Video thumbnail fallback */
.video-thumbnail-fallback {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #2a2a2a;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
}

.video-fallback-icon {
    font-size: 48px;
    color: #666;
    margin-bottom: 8px;
}

.video-fallback-text {
    font-size: 14px;
    color: #999;
    font-weight: 500;
}

/* Video message in own messages */
.message.own .video-message-container {
    background: rgba(0, 132, 255, 0.1);
    border-color: rgba(0, 132, 255, 0.2);
}

/* Video Player Modal */
.video-player-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.video-player-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.video-player-container {
    position: relative;
    background: #1a1a1a;
    border-radius: 12px;
    overflow: hidden;
    max-width: 90vw;
    max-height: 90vh;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.video-player-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #2a2a2a;
    border-bottom: 1px solid #333;
}

.video-player-info h3 {
    color: white;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.video-player-info p {
    color: #ccc;
    margin: 4px 0 0 0;
    font-size: 12px;
}

.video-player-close {
    background: none;
    border: none;
    color: #ccc;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.video-player-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.video-player-content {
    position: relative;
    background: #000;
}

.video-player-content video {
    width: 100%;
    height: auto;
    max-height: 70vh;
    display: block;
}

.video-error-message,
.video-loading-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    background: rgba(0, 0, 0, 0.8);
    padding: 30px;
    border-radius: 12px;
    max-width: 300px;
}

.video-error-message .error-icon {
    font-size: 48px;
    color: #ff6b6b;
    margin-bottom: 15px;
}

.video-error-message h4 {
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: 600;
}

.video-error-message p {
    margin: 0 0 20px 0;
    font-size: 14px;
    color: #ccc;
    line-height: 1.4;
}

.error-download-btn {
    background: #0084ff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
    transition: background-color 0.2s ease;
}

.error-download-btn:hover {
    background: #0066cc;
}

.video-loading-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.loading-spinner {
    font-size: 32px;
    color: #0084ff;
}

.loading-text {
    font-size: 16px;
    color: #ccc;
}

.video-player-controls {
    display: flex;
    gap: 10px;
    padding: 15px 20px;
    background: #2a2a2a;
    border-top: 1px solid #333;
}

.video-control-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.video-control-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.video-control-btn:active {
    transform: scale(0.95);
}

/* Mobile responsive video player */
@media (max-width: 768px) {
    .video-player-modal {
        padding: 10px;
    }

    .video-player-container {
        max-width: 95vw;
        max-height: 95vh;
    }

    .video-player-header {
        padding: 12px 15px;
    }

    .video-player-controls {
        padding: 12px 15px;
        flex-wrap: wrap;
    }

    .video-control-btn {
        flex: 1;
        justify-content: center;
        min-width: 120px;
    }
}

.emoji-picker-header {
    background: #f0f2f5;
    border-bottom: 1px solid #e9edef;
    padding: 8px;
}

.emoji-categories {
    display: flex;
    gap: 4px;
    justify-content: space-around;
}

.emoji-category-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 18px;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.emoji-category-btn:hover {
    background: rgba(0, 0, 0, 0.05);
}

.emoji-category-btn.active {
    background: #0084ff;
    color: white;
}

.emoji-category-btn.active:hover {
    background: #0066cc;
}

.emoji-picker-content {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 4px;
}

.emoji-item {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 20px;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    aspect-ratio: 1;
}

.emoji-item:hover {
    background: rgba(0, 132, 255, 0.1);
    transform: scale(1.1);
}

.emoji-item:active {
    transform: scale(0.95);
}

/* Mobile responsive emoji picker */
@media (max-width: 768px) {
    .emoji-picker {
        left: 10px;
        right: 10px;
        width: auto;
        max-width: 380px;
        height: 350px;
        bottom: 90px;
    }

    .emoji-grid {
        grid-template-columns: repeat(7, 1fr);
    }

    .emoji-item {
        font-size: 22px;
        padding: 10px;
    }

    .emoji-categories {
        gap: 2px;
    }

    .emoji-category-btn {
        width: 36px;
        height: 36px;
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .emoji-picker {
        left: 5px;
        right: 5px;
        height: 320px;
        bottom: 85px;
    }

    .emoji-grid {
        grid-template-columns: repeat(6, 1fr);
        gap: 2px;
    }

    .emoji-item {
        font-size: 20px;
        padding: 8px;
    }
}

/* Reply Display in Messages */
.message-reply {
    background: rgba(0, 132, 255, 0.1);
    border-radius: 8px;
    padding: 8px 12px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    gap: 8px;
    position: relative;
}

.message-reply:hover {
    background: rgba(0, 132, 255, 0.15);
}

.reply-bar {
    width: 3px;
    background: #0084ff;
    border-radius: 2px;
    flex-shrink: 0;
}

.reply-content {
    flex: 1;
    min-width: 0;
}

.reply-sender {
    font-size: 12px;
    font-weight: 600;
    color: #0084ff;
    margin-bottom: 2px;
}

.reply-text {
    font-size: 13px;
    color: #666;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 4px;
}

.reply-text i {
    color: #999;
    font-size: 12px;
}

/* Scroll to message animation */
.message-highlight {
    animation: messageHighlight 2s ease-out;
}

@keyframes messageHighlight {
    0% {
        background-color: rgba(255, 235, 59, 0.3);
    }
    100% {
        background-color: transparent;
    }
}

/* Message Edit Modal */
.edit-message-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.edit-message-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.edit-message-content {
    background: white;
    border-radius: 20px;
    padding: 0;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    position: relative;
    max-width: 500px;
    width: 90%;
    animation: modalSlideUp 0.3s ease-out;
    overflow: hidden;
}

.edit-message-header {
    padding: 1.5rem;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.edit-message-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.2rem;
    font-weight: 600;
}

.close-edit {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-edit:hover {
    background: #f5f5f5;
    color: #666;
}

.edit-message-body {
    padding: 1.5rem;
}

#editMessageInput {
    width: 100%;
    min-height: 120px;
    padding: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    transition: border-color 0.2s ease;
}

#editMessageInput:focus {
    outline: none;
    border-color: #ff6b9d;
    box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
}

.edit-message-info {
    display: flex;
    justify-content: flex-end;
    margin-top: 0.5rem;
}

.char-count {
    font-size: 0.85rem;
    color: #6c757d;
}

.edit-message-footer {
    padding: 1.5rem;
    border-top: 1px solid #f0f0f0;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.btn-primary {
    background: linear-gradient(45deg, #ff6b9d, #c44569);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #c44569, #a73e5c);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
}

/* Call Interface Styles */
.call-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(10px);
}

.call-modal-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    color: white;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 90%;
    position: relative;
}

.active-call .call-modal-content {
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
}

.call-info {
    margin-bottom: 2rem;
}

.caller-avatar, .participant-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 3rem;
    color: white;
    border: 4px solid rgba(255, 255, 255, 0.3);
    overflow: hidden;
}

.caller-avatar img, .participant-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.caller-details h3, .participant-details h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.call-type, .call-status {
    font-size: 1rem;
    opacity: 0.8;
    margin: 0.25rem 0;
}

.call-actions {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.call-btn {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.accept-btn {
    background: linear-gradient(45deg, #4caf50, #45a049);
    color: white;
}

.accept-btn:hover {
    background: linear-gradient(45deg, #45a049, #3d8b40);
    transform: scale(1.1);
}

.decline-btn {
    background: linear-gradient(45deg, #f44336, #d32f2f);
    color: white;
}

.decline-btn:hover {
    background: linear-gradient(45deg, #d32f2f, #b71c1c);
    transform: scale(1.1);
}

/* Active Call Styles */
.call-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.call-participant-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    text-align: left;
}

.participant-avatar {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
}

.participant-details h3 {
    font-size: 1.2rem;
    margin-bottom: 0.25rem;
}

.call-duration {
    font-size: 1rem;
    font-weight: 600;
    color: #4caf50;
}

.minimize-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.minimize-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Video Container */
.video-container {
    position: relative;
    width: 100%;
    height: 300px;
    background: #000;
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 1rem;
}

#remoteVideo {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

#localVideo {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 120px;
    height: 90px;
    border-radius: 10px;
    border: 2px solid rgba(255, 255, 255, 0.5);
    object-fit: cover;
}

/* Audio Indicator */
.audio-indicator {
    display: flex;
    justify-content: center;
    margin-bottom: 1rem;
}

.audio-wave {
    display: flex;
    align-items: center;
    gap: 3px;
    height: 40px;
}

.wave-bar {
    width: 4px;
    background: linear-gradient(45deg, #4caf50, #81c784);
    border-radius: 2px;
    animation: audioWave 1s ease-in-out infinite;
}

.wave-bar:nth-child(2) { animation-delay: 0.1s; }
.wave-bar:nth-child(3) { animation-delay: 0.2s; }
.wave-bar:nth-child(4) { animation-delay: 0.3s; }
.wave-bar:nth-child(5) { animation-delay: 0.4s; }

@keyframes audioWave {
    0%, 100% { height: 10px; }
    50% { height: 30px; }
}

/* Call Controls */
.call-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.control-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.control-btn.active {
    background: linear-gradient(45deg, #4caf50, #45a049);
}

.control-btn.muted {
    background: linear-gradient(45deg, #f44336, #d32f2f);
}

.end-call-btn {
    background: linear-gradient(45deg, #f44336, #d32f2f);
}

.end-call-btn:hover {
    background: linear-gradient(45deg, #d32f2f, #b71c1c);
}

/* Call Minimized Bar */
.call-minimized {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 25px;
    padding: 0.75rem 1rem;
    color: white;
    display: flex;
    align-items: center;
    gap: 1rem;
    z-index: 1500;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.minimized-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.minimized-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    overflow: hidden;
}

.minimized-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.minimized-details {
    display: flex;
    flex-direction: column;
    font-size: 0.9rem;
}

.minimized-duration {
    font-size: 0.8rem;
    opacity: 0.8;
}

.minimized-controls {
    display: flex;
    gap: 0.5rem;
}

.minimized-btn {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.minimized-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.minimized-btn.muted {
    background: linear-gradient(45deg, #f44336, #d32f2f);
}

.end-btn {
    background: linear-gradient(45deg, #f44336, #d32f2f);
}

/* Test Media Button */
.test-media {
    background: linear-gradient(45deg, #2196f3, #1976d2) !important;
}

.test-media:hover {
    background: linear-gradient(45deg, #1976d2, #1565c0) !important;
    transform: scale(1.05);
}

/* Responsive Design for Calls */
@media (max-width: 768px) {
    .call-modal-content {
        padding: 1.5rem;
        margin: 1rem;
    }

    .caller-avatar, .participant-avatar {
        width: 100px;
        height: 100px;
        font-size: 2.5rem;
    }

    .call-btn {
        width: 60px;
        height: 60px;
        font-size: 1.3rem;
    }

    .video-container {
        height: 200px;
    }

    #localVideo {
        width: 80px;
        height: 60px;
    }

    .call-minimized {
        top: 10px;
        right: 10px;
        padding: 0.5rem 0.75rem;
    }

    .minimized-avatar {
        width: 35px;
        height: 35px;
    }

    .minimized-btn {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }
}

/* Connection Status Indicator */
.connection-status {
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    display: inline-block;
    margin-top: 2px;
    transition: all 0.3s ease;
}

.connection-status.connected {
    background: rgba(76, 175, 80, 0.2);
    color: #4caf50;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.connection-status.disconnected {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
    border: 1px solid rgba(244, 67, 54, 0.3);
}

.connection-status.connecting {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.3);
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

/* Call History Messages */
.call-message {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 12px 16px 8px 16px; /* Reduced bottom padding for timestamp */
    margin: 8px 0 0 0; /* Remove bottom margin to let message footer handle spacing */
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.call-message:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.call-info {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
}

.call-icon {
    font-size: 1.2rem;
    opacity: 0.8;
    flex-shrink: 0;
}

.call-details {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
}

.call-text {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    flex: 1;
}

.call-time {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 400;
    flex-shrink: 0;
    margin-left: auto;
}

/* Different call types */
.missed-call {
    background: rgba(244, 67, 54, 0.1);
    border-color: rgba(244, 67, 54, 0.3);
}

.missed-call .call-icon {
    color: #f44336;
}

.missed-call .call-text {
    color: #f44336;
}

.missed-call .call-time {
    color: rgba(244, 67, 54, 0.7);
}

.declined-call {
    background: rgba(255, 152, 0, 0.1);
    border-color: rgba(255, 152, 0, 0.3);
}

.declined-call .call-icon {
    color: #ff9800;
}

.declined-call .call-text {
    color: #ff9800;
}

.declined-call .call-time {
    color: rgba(255, 152, 0, 0.7);
}

.completed-call {
    background: rgba(76, 175, 80, 0.1);
    border-color: rgba(76, 175, 80, 0.3);
}

.completed-call .call-icon {
    color: #4caf50;
}

.completed-call .call-text {
    color: #4caf50;
}

.completed-call .call-time {
    color: rgba(76, 175, 80, 0.7);
}

/* Call messages in own messages */
.message.own .call-message {
    background: rgba(255, 255, 255, 0.2);
    margin-left: auto;
    max-width: 280px;
}

/* Hide message footer timestamp for call messages since we show it inside */
.message-bubble:has(.call-message) .message-footer .message-time {
    display: none;
}

/* Alternative approach for browsers that don't support :has() */
.message-bubble .call-message ~ .message-footer .message-time {
    display: none;
}

/* But keep the status indicator for own call messages */
.message-bubble:has(.call-message) .message-footer {
    justify-content: flex-end;
}

.message-bubble .call-message ~ .message-footer {
    justify-content: flex-end;
}

.message.own .missed-call {
    background: rgba(244, 67, 54, 0.15);
}

.message.own .declined-call {
    background: rgba(255, 152, 0, 0.15);
}

.message.own .completed-call {
    background: rgba(76, 175, 80, 0.15);
}

/* Mobile responsiveness for call messages */
@media (max-width: 768px) {
    .call-message {
        padding: 10px 12px;
        font-size: 0.85rem;
    }

    .call-icon {
        font-size: 1.1rem;
    }

    .message.own .call-message {
        max-width: 240px;
    }
}



